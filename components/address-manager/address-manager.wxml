<!-- 地址管理组件 -->
<view class="address-manager">
  <!-- 地址显示区域 -->
  <view class="address-display" wx:if="{{!showAddressList && !showAddressForm}}" bindtap="showAddressSelector">
    <view class="address-content">
      <view class="address-info" wx:if="{{selectedAddress}}">
        <text class="address-name">{{selectedAddress.name}} {{selectedAddress.phone}}</text>
        <text class="address-detail">{{selectedAddress.province}}{{selectedAddress.city}}{{selectedAddress.district}}{{selectedAddress.detail}}</text>
      </view>
      <text class="address-placeholder" wx:else>{{placeholder || '请选择配送地址'}}</text>
      <text class="address-arrow">></text>
    </view>
  </view>

  <!-- 地址列表弹窗 -->
  <view class="address-modal" wx:if="{{showAddressList}}">
    <view class="modal-mask" bindtap="hideAddressSelector" catchtouchmove="preventTouchMove"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>选择配送地址</text>
        <view class="modal-close" bindtap="hideAddressSelector">×</view>
      </view>
      <scroll-view scroll-y="true" class="address-list">
        <view class="address-item {{selectedAddress && selectedAddress.id === item.id ? 'selected' : ''}}"
              wx:for="{{savedAddresses}}"
              wx:key="id">
          <view class="address-item-content" bindtap="selectAddress" data-id="{{item.id}}">
            <view class="address-item-header">
              <text class="address-name">{{item.name}}</text>
              <text class="address-phone">{{item.phone}}</text>
              <view class="address-default" wx:if="{{item.isDefault}}">默认</view>
            </view>
            <text class="address-detail">{{item.province}}{{item.city}}{{item.district}}{{item.detail}}</text>
          </view>
          <view class="address-actions" catchtap="preventBubble">
            <view class="action-btn edit" bindtap="editAddressWithPlugin" data-id="{{item.id}}">编辑</view>
            <view class="action-btn edit-legacy" bindtap="showEditAddressForm" data-id="{{item.id}}">手动编辑</view>
            <view class="action-btn delete" bindtap="deleteAddress" data-id="{{item.id}}">删除</view>
            <view class="action-btn default" wx:if="{{!item.isDefault}}" bindtap="setDefaultAddress" data-id="{{item.id}}">设为默认</view>
          </view>
        </view>

        <view class="empty-address" wx:if="{{savedAddresses.length === 0}}">
          <text class="empty-text">暂无保存的地址</text>
        </view>
      </scroll-view>

      <view class="address-footer">
        <view class="add-address-btn" bindtap="addAddressWithPlugin">
          <text class="add-icon">+</text>
          <text>新增地址</text>
        </view>
        <view class="add-address-btn-legacy" bindtap="showAddAddressForm">
          <text class="add-icon">✏️</text>
          <text>手动新增</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 地址表单弹窗 -->
  <view class="address-form-modal" wx:if="{{showAddressForm}}">
    <view class="modal-mask" bindtap="hideAddressForm"></view>
    <view class="form-content">
      <view class="form-header">
        <text>{{editingAddress ? '编辑地址' : '新增地址'}}</text>
        <view class="modal-close" bindtap="hideAddressForm">×</view>
      </view>

      <scroll-view scroll-y="true" class="form-scroll">
        <view class="form-group">
          <text class="form-label">收货人</text>
          <input class="form-input"
                 placeholder="请输入收货人姓名"
                 value="{{addressForm.name}}"
                 data-field="name"
                 bindinput="onAddressFormInput"
                 adjust-position="{{false}}" />
        </view>

        <view class="form-group">
          <text class="form-label">手机号码</text>
          <input class="form-input"
                 placeholder="请输入手机号码"
                 type="number"
                 value="{{addressForm.phone}}"
                 data-field="phone"
                 bindinput="onAddressFormInput"
                 adjust-position="{{false}}" />
        </view>

        <view class="form-group">
          <view class="form-label-row">
            <text class="form-label">所在地区</text>
            <view class="location-btn" bindtap="showMapLocationSelector">
              <text class="location-icon">🗺️</text>
              <text>地图选择</text>
            </view>
          </view>
          <picker mode="selector"
                  range="{{guangzhouDistricts}}"
                  value="{{selectedDistrictIndex}}"
                  bindchange="onDistrictChange">
            <view class="picker-content">
              <text class="picker-text" wx:if="{{addressForm.district}}">
                {{addressForm.province}} {{addressForm.city}} {{addressForm.district}}
              </text>
              <text class="picker-placeholder" wx:else>请选择区域</text>
              <text class="picker-arrow">></text>
            </view>
          </picker>
        </view>

        <view class="form-group">
          <text class="form-label">详细地址</text>
          <textarea class="form-textarea"
                    placeholder="请输入详细地址，如街道、楼牌号等"
                    value="{{addressForm.detail}}"
                    data-field="detail"
                    bindinput="onAddressFormInput"
                    maxlength="100"
                    adjust-position="{{false}}" />
        </view>

        <view class="form-group checkbox-group">
          <label class="checkbox-label">
            <checkbox checked="{{addressForm.isDefault}}" bindchange="toggleDefaultAddress" />
            <text>设为默认地址</text>
          </label>
        </view>
      </scroll-view>

      <view class="form-footer">
        <view class="save-btn" bindtap="saveAddress">保存地址</view>
      </view>
    </view>
  </view>

  <!-- 地图选择弹窗 -->
  <view class="map-selector-modal" wx:if="{{showMapSelector}}">
    <view class="map-modal-mask" bindtap="hideMapSelector"></view>
    <view class="map-content">
      <view class="map-header">
        <text>选择位置</text>
        <view class="map-modal-close" bindtap="hideMapSelector">×</view>
      </view>

      <view class="map-container">
        <map
          id="locationMap"
          longitude="{{mapCenter.longitude}}"
          latitude="{{mapCenter.latitude}}"
          scale="{{mapScale}}"
          markers="{{mapMarkers}}"
          show-location="{{true}}"
          enable-zoom="{{true}}"
          enable-scroll="{{true}}"
          enable-rotate="{{false}}"
          bindtap="onMapTap"
          bindregionchange="onMapRegionChange"
          class="location-map">

          <!-- 地图中心十字标记 -->
          <view class="map-center-marker">
            <view class="center-cross"></view>
          </view>
        </map>

        <!-- 地图提示信息 -->
        <view class="map-tip">
          <view class="tip-text">拖动地图或点击选择位置</view>
        </view>
      </view>

      <view class="map-footer">
        <view class="map-cancel-btn" bindtap="hideMapSelector">取消</view>
        <view class="map-confirm-btn" bindtap="confirmMapLocation">确认位置</view>
      </view>
    </view>
  </view>
</view>
